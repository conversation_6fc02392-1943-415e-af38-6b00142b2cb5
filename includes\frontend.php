<?php
/**
 * Frontend Interface - Clean AI Image Tool
 */

defined('ABSPATH') || exit;

require_once AI_STYLED_PATH . 'includes/processor.php';
$processor = new AI_Image_Processor();
$overlays = $processor->get_overlays();
$identity = AIStyledImagePlugin::instance()->get_identity_settings();
?>

<div class="ai-image-tool" data-style="<?php echo esc_attr($atts['style']); ?>">
    <div class="ai-tool-container">
        
        <header class="ai-tool-header">
            <h1 class="ai-tool-title"><?php echo esc_html($identity['brand_name'] ?? 'AI STYLED'); ?> IMAGE STUDIO</h1>
            <?php
            $processing_mode = get_option('ai_styled_processing_mode', 'new');
            $subtitle = $processing_mode === 'new'
                ? 'Transform your photos with AI-powered architectural elements using intelligent prompt generation'
                : 'Transform your photos with AI-powered architectural elements';
            ?>
            <p class="ai-tool-subtitle"><?php echo esc_html($subtitle); ?></p>
        </header>

        <form id="ai-form" class="ai-main-form" enctype="multipart/form-data">
            <div class="ai-form-sections">
                
                <div class="ai-upload-section">
                    <h3>Upload Your Photo</h3>
                    <div class="ai-upload-area" id="upload-zone">
                        <div class="ai-upload-content">
                            <div class="ai-upload-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                            </div>
                            <p class="ai-upload-text">Drop your image here or click to browse</p>
                            <span class="ai-upload-specs">JPG, PNG, WebP • Max 10MB</span>
                        </div>
                        
                        <div class="ai-upload-preview" id="image-preview" style="display: none;">
                            <img id="preview-image" src="" alt="Preview">
                            <div class="ai-preview-details">
                                <span id="image-name" class="ai-image-name"></span>
                                <button type="button" class="ai-change-btn">Change Photo</button>
                            </div>
                        </div>
                        
                        <input type="file" id="user-image" name="user_image" accept="image/*" style="display: none;">
                    </div>
                </div>

                <div class="ai-style-section">
                    <h3>Choose Architecture Style</h3>
                    <div class="ai-styles-container">
                        <input type="hidden" id="selected-overlay" name="overlay_id" value="">
                        
                        <?php if (empty($overlays)): ?>
                            <div class="ai-empty-message">
                                <p>No architectural styles available</p>
                            </div>
                        <?php else: ?>
                            <div class="ai-styles-grid">
                                <?php foreach ($overlays as $overlay): ?>
                                    <div class="ai-style-card" 
                                         data-id="<?php echo esc_attr($overlay->id); ?>" 
                                         data-category="<?php echo esc_attr($overlay->category); ?>">
                                        <div class="ai-style-preview">
                                            <img src="<?php echo esc_url($overlay->image_url); ?>" 
                                                 alt="<?php echo esc_attr($overlay->title); ?>"
                                                 loading="lazy">
                                        </div>
                                        <div class="ai-style-details">
                                            <h4><?php echo esc_html($overlay->title); ?></h4>
                                            <span class="ai-style-category"><?php echo esc_html(ucfirst(str_replace('_', ' ', $overlay->category))); ?></span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="ai-action-section">
                <button type="submit" id="generate-btn" class="ai-generate-btn" disabled>
                    <svg class="ai-btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>
                    </svg>
                    Generate AI Image
                </button>
                <div class="ai-status-message" id="status-text">Select a photo and style to continue</div>
            </div>
        </form>

        <div id="processing-section" class="ai-processing-section" style="display: none;">
            <div class="ai-processing-content">
                <div class="ai-processing-animation">
                    <div class="ai-spinner"></div>
                </div>
                <h3 id="processing-title">Creating your AI image...</h3>
                <p id="processing-description">This usually takes 30-60 seconds</p>
                
                <div class="ai-progress-wrapper">
                    <div class="ai-progress-bar">
                        <div class="ai-progress-fill" id="progress-fill"></div>
                    </div>
                    <span class="ai-progress-percentage" id="progress-text">0%</span>
                </div>
                
                <button type="button" id="cancel-processing" class="ai-cancel-btn">
                    Cancel Processing
                </button>
            </div>
        </div>

        <div id="results-section" class="ai-results-section" style="display: none;">
            <div class="ai-results-header">
                <h3>Your AI-Generated Image</h3>
                <button type="button" id="close-results" class="ai-close-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            
            <div class="ai-results-content">
                <div class="ai-result-display">
                    <img id="result-image" src="" alt="AI Generated Result">
                </div>
                
                <div class="ai-result-actions">
                    <button type="button" id="download-result" class="ai-action-btn ai-btn-primary">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7,10 12,15 17,10"/>
                            <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                        Download Image
                    </button>
                    
                    <button type="button" id="create-another" class="ai-action-btn ai-btn-secondary">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 5v14M5 12h14"/>
                        </svg>
                        Create Another
                    </button>
                </div>
            </div>
        </div>
    </div>
</div> 